import SimpleITK as sitk
import numpy as np
import matplotlib.pyplot as plt
import os

# --- 定义文件路径 ---
# 配准后生成的 .nii.gz 文件
nifti_file_path = "./output/test_MRI/registered_MRI.nii.gz"
# 图片保存的文件夹
output_folder = "./output/test_MRI/preview_snapshots"

# --- 检查路径 ---
if not os.path.exists(nifti_file_path):
    print(f"错误: 找不到 NIfTI 文件: {nifti_file_path}")
    exit()
if not os.path.exists(output_folder):
    os.makedirs(output_folder)

# --- 加载图像并转换为Numpy数组 ---
image = sitk.ReadImage(nifti_file_path)
data = sitk.GetArrayFromImage(image)

# 获取图像的三个维度大小 (Depth, Height, Width)
depth, height, width = data.shape
print(f"图像维度: D={depth}, H={height}, W={width}")

# --- 选择三个有代表性的切片（从中间切）---
slice_axial = data[depth // 2, :, :]      # 横断面 (从Z轴中间切)
slice_coronal = data[:, height // 2, :]    # 冠状面 (从Y轴中间切)
slice_sagittal = data[:, :, width // 2]    # 矢状面 (从X轴中间切)

# --- 绘制并保存图像 ---
print("正在生成预览图...")

# 1. 保存横断面图
plt.figure(figsize=(8, 8))
plt.imshow(slice_axial, cmap='gray', origin='lower')
plt.title("Axial Slice (横断面)")
plt.axis('off')
plt.savefig(os.path.join(output_folder, "preview_axial.png"))
plt.close()

# 2. 保存冠状面图
plt.figure(figsize=(8, 8))
# 需要将冠状面旋转一下以符合习惯
plt.imshow(np.rot90(slice_coronal), cmap='gray', origin='lower')
plt.title("Coronal Slice (冠状面)")
plt.axis('off')
plt.savefig(os.path.join(output_folder, "preview_coronal.png"))
plt.close()

# 3. 保存矢状面图
plt.figure(figsize=(8, 8))
# 需要将矢状面旋转一下
plt.imshow(np.rot90(slice_sagittal), cmap='gray', origin='lower')
plt.title("Sagittal Slice (矢状面)")
plt.axis('off')
plt.savefig(os.path.join(output_folder, "preview_sagittal.png"))
plt.close()

print(f"成功！3个预览图已保存至: {output_folder}")
import SimpleITK as sitk
import os
import sys

# --- 步骤 1: 设置文件路径 ---
# !! 重要 !!
# 请将下面三个路径变量修改为您电脑上的实际文件路径

# 输入: 您的数据文件 (需要被移动和对齐的图像)
# 例如: "D:/MyData/brain_only.nii.gz"
moving_image_path = "D:/Path/To/Your/Data_File.nii.gz"

# 输入: 您的模板文件 (作为参考标准的固定图像)
# 例如: "D:/MyData/AAL-Merged.nii.gz"
fixed_image_path = "D:/Path/To/Your/Template_File.nii.gz"

# 输出: 您希望保存配准后结果的文件夹
# 例如: "D:/MyData/Registration_Output"
output_folder_path = "D:/Path/To/Your/Output_Folder"


# --- 脚本主体 (通常无需修改以下部分) ---

# --- 检查和准备 ---
if not all(os.path.exists(p) for p in [moving_image_path, fixed_image_path]):
    print("错误: 找不到输入的数据文件或模板文件。请仔细检查路径。")
    sys.exit(1)
    
if not os.path.isdir(output_folder_path):
    print(f"输出文件夹不存在，正在创建: {output_folder_path}")
    os.makedirs(output_folder_path)

# --- 步骤 2: 加载图像 ---
print("--- 开始图像配准流程 ---")
print(f"固定图像 (Template): {os.path.basename(fixed_image_path)}")
print(f"移动图像 (Data):    {os.path.basename(moving_image_path)}")

# 以32位浮点数格式读取图像，这对于配准中的插值计算很重要
fixed_image = sitk.ReadImage(fixed_image_path, sitk.sitkFloat32)
moving_image = sitk.ReadImage(moving_image_path, sitk.sitkFloat32)


# --- 步骤 3: 初始化变换 ---
# 我们使用仿射变换，它可以处理平移、旋转、缩放和切变。
# 使用CenteredTransformInitializer可以基于图像的几何中心进行初步对齐，为优化器提供一个好的起点。
initial_transform = sitk.CenteredTransformInitializer(
    fixed_image,
    moving_image,
    sitk.AffineTransform(3), # 3表示3D图像
    sitk.CenteredTransformInitializerFilter.GEOMETRY,
)

# --- 步骤 4: 设置详细的配准方法 ---
registration_method = sitk.ImageRegistrationMethod()

# A. 设置度量 (Metric)
# MattesMutualInformation (互信息) 是用于不同个体、不同扫描仪甚至不同模态图像配准的黄金标准。
registration_method.SetMetricAsMattesMutualInformation(numberOfHistogramBins=50)
registration_method.SetMetricSamplingStrategy(registration_method.RANDOM)
registration_method.SetMetricSamplingPercentage(0.01)

# B. 设置插值器 (Interpolator)
# 我们使用线性插值，它在速度和精度之间取得了很好的平衡。
registration_method.SetInterpolator(sitk.sitkLinear)

# C. 设置优化器 (Optimizer)
# 我们使用梯度下降法。学习率和迭代次数是关键参数。
registration_method.SetOptimizerAsGradientDescent(
    learningRate=1.0,
    numberOfIterations=200, # 对于简单情况200次足够, 复杂情况可增加至500或更多
    convergenceMinimumValue=1e-6,
    convergenceWindowSize=10,
)
registration_method.SetOptimizerScalesFromPhysicalShift()

# D. 设置多分辨率金字塔策略
# 这是提高配准速度和鲁棒性的关键。从粗糙的分辨率开始，逐步到精细的分辨率。
registration_method.SetShrinkFactorsPerLevel(shrinkFactors=[4, 2, 1])
registration_method.SetSmoothingSigmasPerLevel(smoothingSigmas=[2, 1, 0])
registration_method.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()

# E. 连接所有组件
registration_method.SetInitialTransform(initial_transform)

# F. (可选) 连接一个命令来在过程中打印进度
def print_progress(registration_method):
    print(f"迭代: {registration_method.GetOptimizerIteration():3} | " + \
          f"度量值: {registration_method.GetMetricValue():10.5f}")

registration_method.AddCommand(sitk.sitkIterationEvent, lambda: print_progress(registration_method))


# --- 步骤 5: 执行配准 ---
print("\n--- 开始执行配准优化... ---")
final_transform = registration_method.Execute(fixed_image, moving_image)
print("--- 配准优化完成! ---")

# 打印最终结果
print(f"优化器停止条件: {registration_method.GetOptimizerStopConditionDescription()}")
print(f"最终度量值: {registration_method.GetMetricValue()}")


# --- 步骤 6: 应用变换并保存结果 ---
# 配准的结果是“变换”本身。我们需要将这个变换应用到移动图像上，
# 将其重采样到与固定图像相同的空间中。
resampler = sitk.ResampleImageFilter()
resampler.SetReferenceImage(fixed_image) # 重要: 输出图像的空间属性(大小,原点,间距)将和固定图像一致
resampler.SetInterpolator(sitk.sitkLinear)
resampler.SetDefaultPixelValue(0) # 将移动图像之外的区域设置为黑色
resampler.SetTransform(final_transform)

resampled_image = resampler.Execute(moving_image)

# 定义输出文件名
output_registered_image_path = os.path.join(output_folder_path, "registered_image.nii.gz")
output_transform_path = os.path.join(output_folder_path, "final_transform.tfm")

# 保存重采样后的图像和计算出的变换文件
sitk.WriteImage(resampled_image, output_registered_image_path)
sitk.WriteTransform(final_transform, output_transform_path)

print("\n--- 所有操作完成 ---")
print(f"配准后的图像已保存至: {output_registered_image_path}")
print(f"计算出的变换文件已保存至: {output_transform_path}")
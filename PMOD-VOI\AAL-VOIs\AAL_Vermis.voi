#VOIS STRUCTURE DEFINITION [3.3002]
#VOLUME SIZES:
91 # number_of_columns_x
109 # number_of_rows_y
91 # number_of_slices_z
1 # number_of_frames_ntimes
#<PERSON><PERSON><PERSON> SIZES IN MM:
2.0 # x_pixel_size
2.0 # y_pixel_size
2.0 # z_pixel_size
#ORIGIN LOCATION IN MM:
90.0 # x_origin_location
90.0 # y_origin_location
74.0 # z_origin_location
#NUMBER OF VOI TIME LISTS:
8
#VOI TIME LIST DEFINITION:
#VOI TIME LIST NUMBER 108 DEFINITION:
108 2 # voi_time_list_index voi_direction
Vermis_1_2_o # voi_name
1 true 0 0 255 false 1.0 false 137.0 -137.0 false false 0 1  # time_list_state removable_flag r_color g_color b_color is_tape_flag tape_width is_sector sector_start_angle sector_end_angle is_mask is_time_list first_volume_time number_of_sectors sec_1_start_angle sec_1_stop_angle .. sec_N_start_angle sec_N_stop_angle
false false # is_in_group is_mesh
1 # number_of_vois
#VOIS DEFINITION:
#TIME VOI NUMBER 109 DEFINITION:
109 # voi_index(1 ..)
109 # voi_code_(voi_index-1)
0 # voi_state_(0=OK)
true # is_voi_in_group
5 # number_of_rois
0.0 # time_in_seconds start_time: 0.0 end_time: 0.0
#ROIS DEFINITION:
#ROI ON SLICE 25 DEFINITION:
24 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -40.0 -26.0
-4.0 -44.0 -26.0
-6.0 -44.0 -26.0
-6.0 -48.0 -26.0
4.0 -48.0 -26.0
4.0 -42.0 -26.0
2.0 -42.0 -26.0
2.0 -40.0 -26.0
-4.0 -40.0 -26.0
#ROI ON SLICE 26 DEFINITION:
25 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
14 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -38.0 -24.0
-4.0 -40.0 -24.0
-6.0 -40.0 -24.0
-6.0 -44.0 -24.0
-2.0 -44.0 -24.0
-2.0 -42.0 -24.0
0.0 -42.0 -24.0
0.0 -44.0 -24.0
6.0 -44.0 -24.0
6.0 -40.0 -24.0
4.0 -40.0 -24.0
4.0 -38.0 -24.0
-4.0 -38.0 -24.0
#ROI ON SLICE 27 DEFINITION:
26 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
14 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -36.0 -22.0
-4.0 -38.0 -22.0
-6.0 -38.0 -22.0
-6.0 -42.0 -22.0
-4.0 -42.0 -22.0
-4.0 -40.0 -22.0
0.0 -40.0 -22.0
0.0 -42.0 -22.0
4.0 -42.0 -22.0
4.0 -38.0 -22.0
2.0 -38.0 -22.0
2.0 -36.0 -22.0
-4.0 -36.0 -22.0
#ROI ON SLICE 28 DEFINITION:
27 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
8 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -36.0 -20.0
-4.0 -40.0 -20.0
4.0 -40.0 -20.0
4.0 -38.0 -20.0
2.0 -38.0 -20.0
2.0 -36.0 -20.0
-4.0 -36.0 -20.0
#ROI ON SLICE 29 DEFINITION:
28 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
6 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -36.0 -18.0
-4.0 -38.0 -18.0
2.0 -38.0 -18.0
2.0 -36.0 -18.0
-4.0 -36.0 -18.0
#VOI TIME LIST NUMBER 109 DEFINITION:
109 2 # voi_time_list_index voi_direction
Vermis_3_o # voi_name
1 true 0 255 255 false 1.0 false 137.0 -137.0 false false 0 1  # time_list_state removable_flag r_color g_color b_color is_tape_flag tape_width is_sector sector_start_angle sector_end_angle is_mask is_time_list first_volume_time number_of_sectors sec_1_start_angle sec_1_stop_angle .. sec_N_start_angle sec_N_stop_angle
false false # is_in_group is_mesh
1 # number_of_vois
#VOIS DEFINITION:
#TIME VOI NUMBER 110 DEFINITION:
110 # voi_index(1 ..)
110 # voi_code_(voi_index-1)
0 # voi_state_(0=OK)
true # is_voi_in_group
11 # number_of_rois
0.0 # time_in_seconds start_time: 0.0 end_time: 0.0
#ROIS DEFINITION:
#ROI ON SLICE 26 DEFINITION:
25 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-2.0 -42.0 -24.0
-2.0 -44.0 -24.0
-6.0 -44.0 -24.0
-6.0 -48.0 -24.0
-4.0 -48.0 -24.0
-4.0 -50.0 -24.0
4.0 -50.0 -24.0
4.0 -44.0 -24.0
0.0 -44.0 -24.0
0.0 -42.0 -24.0
-2.0 -42.0 -24.0
#ROI ON SLICE 27 DEFINITION:
26 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
14 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -40.0 -22.0
-4.0 -42.0 -22.0
-6.0 -42.0 -22.0
-6.0 -48.0 -22.0
-4.0 -48.0 -22.0
-4.0 -50.0 -22.0
2.0 -50.0 -22.0
2.0 -48.0 -22.0
4.0 -48.0 -22.0
4.0 -42.0 -22.0
0.0 -42.0 -22.0
0.0 -40.0 -22.0
-4.0 -40.0 -22.0
#ROI ON SLICE 28 DEFINITION:
27 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -38.0 -20.0
-6.0 -48.0 -20.0
0.0 -48.0 -20.0
0.0 -50.0 -20.0
2.0 -50.0 -20.0
2.0 -48.0 -20.0
4.0 -48.0 -20.0
4.0 -40.0 -20.0
-4.0 -40.0 -20.0
-4.0 -38.0 -20.0
-6.0 -38.0 -20.0
#ROI ON SLICE 29 DEFINITION:
28 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
8 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -36.0 -18.0
-6.0 -48.0 -18.0
4.0 -48.0 -18.0
4.0 -38.0 -18.0
-4.0 -38.0 -18.0
-4.0 -36.0 -18.0
-6.0 -36.0 -18.0
#ROI ON SLICE 30 DEFINITION:
29 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -36.0 -16.0
-6.0 -48.0 -16.0
-4.0 -48.0 -16.0
-4.0 -46.0 -16.0
0.0 -46.0 -16.0
0.0 -48.0 -16.0
4.0 -48.0 -16.0
4.0 -36.0 -16.0
-6.0 -36.0 -16.0
#ROI ON SLICE 31 DEFINITION:
30 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -36.0 -14.0
-6.0 -46.0 -14.0
0.0 -46.0 -14.0
0.0 -48.0 -14.0
2.0 -48.0 -14.0
2.0 -46.0 -14.0
4.0 -46.0 -14.0
4.0 -36.0 -14.0
-6.0 -36.0 -14.0
#ROI ON SLICE 32 DEFINITION:
31 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -36.0 -12.0
-6.0 -46.0 -12.0
-4.0 -46.0 -12.0
-4.0 -48.0 -12.0
2.0 -48.0 -12.0
2.0 -46.0 -12.0
4.0 -46.0 -12.0
4.0 -38.0 -12.0
2.0 -38.0 -12.0
2.0 -36.0 -12.0
-6.0 -36.0 -12.0
#ROI ON SLICE 33 DEFINITION:
32 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
8 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -36.0 -10.0
-6.0 -46.0 -10.0
2.0 -46.0 -10.0
2.0 -44.0 -10.0
4.0 -44.0 -10.0
4.0 -36.0 -10.0
-6.0 -36.0 -10.0
#ROI ON SLICE 34 DEFINITION:
33 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -36.0 -8.0
-6.0 -44.0 -8.0
-2.0 -44.0 -8.0
-2.0 -46.0 -8.0
0.0 -46.0 -8.0
0.0 -44.0 -8.0
2.0 -44.0 -8.0
2.0 -42.0 -8.0
4.0 -42.0 -8.0
4.0 -36.0 -8.0
-6.0 -36.0 -8.0
#ROI ON SLICE 35 DEFINITION:
34 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
14 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -36.0 -6.0
-4.0 -38.0 -6.0
-6.0 -38.0 -6.0
-6.0 -42.0 -6.0
-4.0 -42.0 -6.0
-4.0 -44.0 -6.0
2.0 -44.0 -6.0
2.0 -42.0 -6.0
4.0 -42.0 -6.0
4.0 -38.0 -6.0
2.0 -38.0 -6.0
2.0 -36.0 -6.0
-4.0 -36.0 -6.0
#ROI ON SLICE 36 DEFINITION:
35 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-2.0 -40.0 -4.0
-2.0 -42.0 -4.0
-4.0 -42.0 -4.0
-4.0 -44.0 -4.0
2.0 -44.0 -4.0
2.0 -42.0 -4.0
0.0 -42.0 -4.0
0.0 -40.0 -4.0
-2.0 -40.0 -4.0
#VOI TIME LIST NUMBER 110 DEFINITION:
110 2 # voi_time_list_index voi_direction
Vermis_4_5_o # voi_name
1 true 0 255 0 false 1.0 false 137.0 -137.0 false false 0 1  # time_list_state removable_flag r_color g_color b_color is_tape_flag tape_width is_sector sector_start_angle sector_end_angle is_mask is_time_list first_volume_time number_of_sectors sec_1_start_angle sec_1_stop_angle .. sec_N_start_angle sec_N_stop_angle
false false # is_in_group is_mesh
1 # number_of_vois
#VOIS DEFINITION:
#TIME VOI NUMBER 111 DEFINITION:
111 # voi_index(1 ..)
111 # voi_code_(voi_index-1)
0 # voi_state_(0=OK)
true # is_voi_in_group
16 # number_of_rois
0.0 # time_in_seconds start_time: 0.0 end_time: 0.0
#ROIS DEFINITION:
#ROI ON SLICE 25 DEFINITION:
24 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-8.0 -52.0 -26.0
-8.0 -60.0 -26.0
-6.0 -60.0 -26.0
-6.0 -58.0 -26.0
-4.0 -58.0 -26.0
-4.0 -56.0 -26.0
2.0 -56.0 -26.0
2.0 -58.0 -26.0
6.0 -58.0 -26.0
6.0 -52.0 -26.0
-8.0 -52.0 -26.0
#ROI ON SLICE 26 DEFINITION:
25 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
16 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -48.0 -24.0
-6.0 -50.0 -24.0
-8.0 -50.0 -24.0
-8.0 -54.0 -24.0
-6.0 -54.0 -24.0
-6.0 -58.0 -24.0
-4.0 -58.0 -24.0
-4.0 -56.0 -24.0
2.0 -56.0 -24.0
2.0 -58.0 -24.0
6.0 -58.0 -24.0
6.0 -50.0 -24.0
-4.0 -50.0 -24.0
-4.0 -48.0 -24.0
-6.0 -48.0 -24.0
#ROI ON SLICE 27 DEFINITION:
26 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
14 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -48.0 -22.0
-6.0 -60.0 -22.0
-2.0 -60.0 -22.0
-2.0 -58.0 -22.0
2.0 -58.0 -22.0
2.0 -60.0 -22.0
4.0 -60.0 -22.0
4.0 -48.0 -22.0
2.0 -48.0 -22.0
2.0 -50.0 -22.0
-4.0 -50.0 -22.0
-4.0 -48.0 -22.0
-6.0 -48.0 -22.0
#ROI ON SLICE 28 DEFINITION:
27 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
14 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -48.0 -20.0
-6.0 -62.0 -20.0
0.0 -62.0 -20.0
0.0 -60.0 -20.0
2.0 -60.0 -20.0
2.0 -62.0 -20.0
4.0 -62.0 -20.0
4.0 -48.0 -20.0
2.0 -48.0 -20.0
2.0 -50.0 -20.0
0.0 -50.0 -20.0
0.0 -48.0 -20.0
-6.0 -48.0 -20.0
#ROI ON SLICE 29 DEFINITION:
28 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -48.0 -18.0
-6.0 -64.0 -18.0
-4.0 -64.0 -18.0
-4.0 -62.0 -18.0
2.0 -62.0 -18.0
2.0 -64.0 -18.0
4.0 -64.0 -18.0
4.0 -48.0 -18.0
-6.0 -48.0 -18.0
#ROI ON SLICE 30 DEFINITION:
29 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -46.0 -16.0
-4.0 -48.0 -16.0
-6.0 -48.0 -16.0
-6.0 -64.0 -16.0
4.0 -64.0 -16.0
4.0 -48.0 -16.0
0.0 -48.0 -16.0
0.0 -46.0 -16.0
-4.0 -46.0 -16.0
#ROI ON SLICE 31 DEFINITION:
30 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -46.0 -14.0
-6.0 -66.0 -14.0
-4.0 -66.0 -14.0
-4.0 -64.0 -14.0
4.0 -64.0 -14.0
4.0 -46.0 -14.0
2.0 -46.0 -14.0
2.0 -48.0 -14.0
0.0 -48.0 -14.0
0.0 -46.0 -14.0
-6.0 -46.0 -14.0
#ROI ON SLICE 32 DEFINITION:
31 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -46.0 -12.0
-6.0 -66.0 -12.0
0.0 -66.0 -12.0
0.0 -64.0 -12.0
4.0 -64.0 -12.0
4.0 -46.0 -12.0
2.0 -46.0 -12.0
2.0 -48.0 -12.0
-4.0 -48.0 -12.0
-4.0 -46.0 -12.0
-6.0 -46.0 -12.0
#ROI ON SLICE 33 DEFINITION:
32 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
2.0 -44.0 -10.0
2.0 -46.0 -10.0
-6.0 -46.0 -10.0
-6.0 -66.0 -10.0
2.0 -66.0 -10.0
2.0 -64.0 -10.0
4.0 -64.0 -10.0
4.0 -44.0 -10.0
2.0 -44.0 -10.0
#ROI ON SLICE 34 DEFINITION:
33 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
2.0 -42.0 -8.0
2.0 -44.0 -8.0
0.0 -44.0 -8.0
0.0 -46.0 -8.0
-2.0 -46.0 -8.0
-2.0 -44.0 -8.0
-6.0 -44.0 -8.0
-6.0 -66.0 -8.0
4.0 -66.0 -8.0
4.0 -42.0 -8.0
2.0 -42.0 -8.0
#ROI ON SLICE 35 DEFINITION:
34 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -42.0 -6.0
-6.0 -66.0 -6.0
-4.0 -66.0 -6.0
-4.0 -68.0 -6.0
4.0 -68.0 -6.0
4.0 -42.0 -6.0
2.0 -42.0 -6.0
2.0 -44.0 -6.0
-4.0 -44.0 -6.0
-4.0 -42.0 -6.0
-6.0 -42.0 -6.0
#ROI ON SLICE 36 DEFINITION:
35 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
18 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
2.0 -42.0 -4.0
2.0 -44.0 -4.0
-6.0 -44.0 -4.0
-6.0 -60.0 -4.0
-4.0 -60.0 -4.0
-4.0 -64.0 -4.0
-2.0 -64.0 -4.0
-2.0 -66.0 -4.0
0.0 -66.0 -4.0
0.0 -68.0 -4.0
4.0 -68.0 -4.0
4.0 -66.0 -4.0
6.0 -66.0 -4.0
6.0 -62.0 -4.0
4.0 -62.0 -4.0
4.0 -42.0 -4.0
2.0 -42.0 -4.0
#ROI ON SLICE 37 DEFINITION:
36 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
16 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -42.0 -2.0
-4.0 -44.0 -2.0
-6.0 -44.0 -2.0
-6.0 -58.0 -2.0
-4.0 -58.0 -2.0
-4.0 -64.0 -2.0
-2.0 -64.0 -2.0
-2.0 -66.0 -2.0
4.0 -66.0 -2.0
4.0 -62.0 -2.0
6.0 -62.0 -2.0
6.0 -60.0 -2.0
4.0 -60.0 -2.0
4.0 -42.0 -2.0
-4.0 -42.0 -2.0
#ROI ON SLICE 38 DEFINITION:
37 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
24 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -42.0 0.0
-4.0 -44.0 0.0
-6.0 -44.0 0.0
-6.0 -46.0 0.0
-8.0 -46.0 0.0
-8.0 -50.0 0.0
-6.0 -50.0 0.0
-6.0 -58.0 0.0
-4.0 -58.0 0.0
-4.0 -62.0 0.0
-2.0 -62.0 0.0
-2.0 -64.0 0.0
2.0 -64.0 0.0
2.0 -62.0 0.0
4.0 -62.0 0.0
4.0 -56.0 0.0
6.0 -56.0 0.0
6.0 -46.0 0.0
4.0 -46.0 0.0
4.0 -44.0 0.0
2.0 -44.0 0.0
2.0 -42.0 0.0
-4.0 -42.0 0.0
#ROI ON SLICE 39 DEFINITION:
38 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
20 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-2.0 -44.0 2.0
-2.0 -46.0 2.0
-6.0 -46.0 2.0
-6.0 -56.0 2.0
-4.0 -56.0 2.0
-4.0 -60.0 2.0
-2.0 -60.0 2.0
-2.0 -62.0 2.0
2.0 -62.0 2.0
2.0 -60.0 2.0
4.0 -60.0 2.0
4.0 -52.0 2.0
6.0 -52.0 2.0
6.0 -48.0 2.0
4.0 -48.0 2.0
4.0 -46.0 2.0
0.0 -46.0 2.0
0.0 -44.0 2.0
-2.0 -44.0 2.0
#ROI ON SLICE 40 DEFINITION:
39 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
18 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-2.0 -46.0 4.0
-2.0 -48.0 4.0
-4.0 -48.0 4.0
-4.0 -50.0 4.0
-6.0 -50.0 4.0
-6.0 -56.0 4.0
-4.0 -56.0 4.0
-4.0 -58.0 4.0
-2.0 -58.0 4.0
-2.0 -60.0 4.0
2.0 -60.0 4.0
2.0 -56.0 4.0
4.0 -56.0 4.0
4.0 -48.0 4.0
0.0 -48.0 4.0
0.0 -46.0 4.0
-2.0 -46.0 4.0
#VOI TIME LIST NUMBER 111 DEFINITION:
111 2 # voi_time_list_index voi_direction
Vermis_6_o # voi_name
1 true 255 0 255 false 1.0 false 137.0 -137.0 false false 0 1  # time_list_state removable_flag r_color g_color b_color is_tape_flag tape_width is_sector sector_start_angle sector_end_angle is_mask is_time_list first_volume_time number_of_sectors sec_1_start_angle sec_1_stop_angle .. sec_N_start_angle sec_N_stop_angle
false false # is_in_group is_mesh
1 # number_of_vois
#VOIS DEFINITION:
#TIME VOI NUMBER 112 DEFINITION:
112 # voi_index(1 ..)
112 # voi_code_(voi_index-1)
0 # voi_state_(0=OK)
true # is_voi_in_group
10 # number_of_rois
0.0 # time_in_seconds start_time: 0.0 end_time: 0.0
#ROIS DEFINITION:
#ROI ON SLICE 25 DEFINITION:
24 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
16 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -56.0 -26.0
-4.0 -58.0 -26.0
-6.0 -58.0 -26.0
-6.0 -60.0 -26.0
-8.0 -60.0 -26.0
-8.0 -62.0 -26.0
-6.0 -62.0 -26.0
-6.0 -72.0 -26.0
4.0 -72.0 -26.0
4.0 -68.0 -26.0
6.0 -68.0 -26.0
6.0 -58.0 -26.0
2.0 -58.0 -26.0
2.0 -56.0 -26.0
-4.0 -56.0 -26.0
#ROI ON SLICE 26 DEFINITION:
25 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -56.0 -24.0
-4.0 -58.0 -24.0
-6.0 -58.0 -24.0
-6.0 -74.0 -24.0
4.0 -74.0 -24.0
4.0 -62.0 -24.0
6.0 -62.0 -24.0
6.0 -58.0 -24.0
2.0 -58.0 -24.0
2.0 -56.0 -24.0
-4.0 -56.0 -24.0
#ROI ON SLICE 27 DEFINITION:
26 2 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-2.0 -58.0 -22.0
-2.0 -60.0 -22.0
-6.0 -60.0 -22.0
-6.0 -78.0 -22.0
-4.0 -78.0 -22.0
-4.0 -76.0 -22.0
4.0 -76.0 -22.0
4.0 -60.0 -22.0
2.0 -60.0 -22.0
2.0 -58.0 -22.0
-2.0 -58.0 -22.0
1 # roi_state_(1=FINISHED)
6 137.0 -137.0 false 0 " Contour 2 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -82.0 -22.0
-6.0 -84.0 -22.0
-4.0 -84.0 -22.0
-4.0 -82.0 -22.0
-6.0 -82.0 -22.0
#ROI ON SLICE 28 DEFINITION:
27 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
16 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
0.0 -60.0 -20.0
0.0 -62.0 -20.0
-6.0 -62.0 -20.0
-6.0 -84.0 -20.0
-4.0 -84.0 -20.0
-4.0 -82.0 -20.0
-2.0 -82.0 -20.0
-2.0 -78.0 -20.0
2.0 -78.0 -20.0
2.0 -80.0 -20.0
4.0 -80.0 -20.0
4.0 -62.0 -20.0
2.0 -62.0 -20.0
2.0 -60.0 -20.0
0.0 -60.0 -20.0
#ROI ON SLICE 29 DEFINITION:
28 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
16 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -62.0 -18.0
-4.0 -64.0 -18.0
-6.0 -64.0 -18.0
-6.0 -84.0 -18.0
-4.0 -84.0 -18.0
-4.0 -82.0 -18.0
-2.0 -82.0 -18.0
-2.0 -80.0 -18.0
2.0 -80.0 -18.0
2.0 -82.0 -18.0
4.0 -82.0 -18.0
4.0 -64.0 -18.0
2.0 -64.0 -18.0
2.0 -62.0 -18.0
-4.0 -62.0 -18.0
#ROI ON SLICE 30 DEFINITION:
29 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
8 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -64.0 -16.0
-6.0 -80.0 -16.0
2.0 -80.0 -16.0
2.0 -82.0 -16.0
4.0 -82.0 -16.0
4.0 -64.0 -16.0
-6.0 -64.0 -16.0
#ROI ON SLICE 31 DEFINITION:
30 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -64.0 -14.0
-4.0 -66.0 -14.0
-6.0 -66.0 -14.0
-6.0 -76.0 -14.0
-4.0 -76.0 -14.0
-4.0 -78.0 -14.0
2.0 -78.0 -14.0
2.0 -80.0 -14.0
4.0 -80.0 -14.0
4.0 -64.0 -14.0
-4.0 -64.0 -14.0
#ROI ON SLICE 32 DEFINITION:
31 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
0.0 -64.0 -12.0
0.0 -66.0 -12.0
-6.0 -66.0 -12.0
-6.0 -74.0 -12.0
-4.0 -74.0 -12.0
-4.0 -78.0 -12.0
4.0 -78.0 -12.0
4.0 -64.0 -12.0
0.0 -64.0 -12.0
#ROI ON SLICE 33 DEFINITION:
32 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
14 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
2.0 -64.0 -10.0
2.0 -66.0 -10.0
-6.0 -66.0 -10.0
-6.0 -70.0 -10.0
-4.0 -70.0 -10.0
-4.0 -74.0 -10.0
-2.0 -74.0 -10.0
-2.0 -76.0 -10.0
2.0 -76.0 -10.0
2.0 -74.0 -10.0
4.0 -74.0 -10.0
4.0 -64.0 -10.0
2.0 -64.0 -10.0
#ROI ON SLICE 34 DEFINITION:
33 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -66.0 -8.0
-6.0 -68.0 -8.0
-4.0 -68.0 -8.0
-4.0 -72.0 -8.0
-2.0 -72.0 -8.0
-2.0 -74.0 -8.0
2.0 -74.0 -8.0
2.0 -72.0 -8.0
6.0 -72.0 -8.0
6.0 -66.0 -8.0
-6.0 -66.0 -8.0
#VOI TIME LIST NUMBER 112 DEFINITION:
112 2 # voi_time_list_index voi_direction
Vermis_7_o # voi_name
1 true 255 200 0 false 1.0 false 137.0 -137.0 false false 0 1  # time_list_state removable_flag r_color g_color b_color is_tape_flag tape_width is_sector sector_start_angle sector_end_angle is_mask is_time_list first_volume_time number_of_sectors sec_1_start_angle sec_1_stop_angle .. sec_N_start_angle sec_N_stop_angle
false false # is_in_group is_mesh
1 # number_of_vois
#VOIS DEFINITION:
#TIME VOI NUMBER 113 DEFINITION:
113 # voi_index(1 ..)
113 # voi_code_(voi_index-1)
0 # voi_state_(0=OK)
true # is_voi_in_group
10 # number_of_rois
0.0 # time_in_seconds start_time: 0.0 end_time: 0.0
#ROIS DEFINITION:
#ROI ON SLICE 19 DEFINITION:
18 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
6 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-2.0 -76.0 -38.0
-2.0 -78.0 -38.0
0.0 -78.0 -38.0
0.0 -76.0 -38.0
-2.0 -76.0 -38.0
#ROI ON SLICE 20 DEFINITION:
19 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
8 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -74.0 -36.0
-4.0 -78.0 -36.0
0.0 -78.0 -36.0
0.0 -76.0 -36.0
2.0 -76.0 -36.0
2.0 -74.0 -36.0
-4.0 -74.0 -36.0
#ROI ON SLICE 21 DEFINITION:
20 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -70.0 -34.0
-4.0 -78.0 -34.0
2.0 -78.0 -34.0
2.0 -76.0 -34.0
4.0 -76.0 -34.0
4.0 -72.0 -34.0
-2.0 -72.0 -34.0
-2.0 -70.0 -34.0
-4.0 -70.0 -34.0
#ROI ON SLICE 22 DEFINITION:
21 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -68.0 -32.0
-4.0 -80.0 -32.0
2.0 -80.0 -32.0
2.0 -78.0 -32.0
4.0 -78.0 -32.0
4.0 -70.0 -32.0
-2.0 -70.0 -32.0
-2.0 -68.0 -32.0
-4.0 -68.0 -32.0
#ROI ON SLICE 23 DEFINITION:
22 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-8.0 -66.0 -30.0
-8.0 -74.0 -30.0
-6.0 -74.0 -30.0
-6.0 -80.0 -30.0
4.0 -80.0 -30.0
4.0 -78.0 -30.0
6.0 -78.0 -30.0
6.0 -66.0 -30.0
-8.0 -66.0 -30.0
#ROI ON SLICE 24 DEFINITION:
23 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
16 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -62.0 -28.0
-6.0 -64.0 -28.0
-8.0 -64.0 -28.0
-8.0 -68.0 -28.0
-6.0 -68.0 -28.0
-6.0 -82.0 -28.0
0.0 -82.0 -28.0
0.0 -80.0 -28.0
4.0 -80.0 -28.0
4.0 -70.0 -28.0
6.0 -70.0 -28.0
6.0 -64.0 -28.0
4.0 -64.0 -28.0
4.0 -62.0 -28.0
-6.0 -62.0 -28.0
#ROI ON SLICE 25 DEFINITION:
24 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -72.0 -26.0
-6.0 -80.0 -26.0
-4.0 -80.0 -26.0
-4.0 -84.0 -26.0
-2.0 -84.0 -26.0
-2.0 -82.0 -26.0
2.0 -82.0 -26.0
2.0 -80.0 -26.0
4.0 -80.0 -26.0
4.0 -72.0 -26.0
-6.0 -72.0 -26.0
#ROI ON SLICE 26 DEFINITION:
25 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -74.0 -24.0
-6.0 -80.0 -24.0
-4.0 -80.0 -24.0
-4.0 -82.0 -24.0
0.0 -82.0 -24.0
0.0 -84.0 -24.0
2.0 -84.0 -24.0
2.0 -78.0 -24.0
4.0 -78.0 -24.0
4.0 -74.0 -24.0
-6.0 -74.0 -24.0
#ROI ON SLICE 27 DEFINITION:
26 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -76.0 -22.0
-4.0 -78.0 -22.0
-6.0 -78.0 -22.0
-6.0 -82.0 -22.0
2.0 -82.0 -22.0
2.0 -78.0 -22.0
4.0 -78.0 -22.0
4.0 -76.0 -22.0
-4.0 -76.0 -22.0
#ROI ON SLICE 28 DEFINITION:
27 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
6 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-2.0 -78.0 -20.0
-2.0 -82.0 -20.0
2.0 -82.0 -20.0
2.0 -78.0 -20.0
-2.0 -78.0 -20.0
#VOI TIME LIST NUMBER 113 DEFINITION:
113 2 # voi_time_list_index voi_direction
Vermis_8_o # voi_name
1 true 255 0 0 false 1.0 false 137.0 -137.0 false false 0 1  # time_list_state removable_flag r_color g_color b_color is_tape_flag tape_width is_sector sector_start_angle sector_end_angle is_mask is_time_list first_volume_time number_of_sectors sec_1_start_angle sec_1_stop_angle .. sec_N_start_angle sec_N_stop_angle
false false # is_in_group is_mesh
1 # number_of_vois
#VOIS DEFINITION:
#TIME VOI NUMBER 114 DEFINITION:
114 # voi_index(1 ..)
114 # voi_code_(voi_index-1)
0 # voi_state_(0=OK)
true # is_voi_in_group
10 # number_of_rois
0.0 # time_in_seconds start_time: 0.0 end_time: 0.0
#ROIS DEFINITION:
#ROI ON SLICE 15 DEFINITION:
14 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -64.0 -46.0
-4.0 -66.0 -46.0
-6.0 -66.0 -46.0
-6.0 -72.0 -46.0
-4.0 -72.0 -46.0
-4.0 -68.0 -46.0
0.0 -68.0 -46.0
0.0 -70.0 -46.0
4.0 -70.0 -46.0
4.0 -64.0 -46.0
-4.0 -64.0 -46.0
#ROI ON SLICE 16 DEFINITION:
15 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -64.0 -44.0
-6.0 -74.0 -44.0
-2.0 -74.0 -44.0
-2.0 -72.0 -44.0
0.0 -72.0 -44.0
0.0 -74.0 -44.0
4.0 -74.0 -44.0
4.0 -64.0 -44.0
-6.0 -64.0 -44.0
#ROI ON SLICE 17 DEFINITION:
16 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -64.0 -42.0
-6.0 -74.0 -42.0
-4.0 -74.0 -42.0
-4.0 -76.0 -42.0
2.0 -76.0 -42.0
2.0 -74.0 -42.0
4.0 -74.0 -42.0
4.0 -64.0 -42.0
-6.0 -64.0 -42.0
#ROI ON SLICE 18 DEFINITION:
17 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -62.0 -40.0
-4.0 -64.0 -40.0
-6.0 -64.0 -40.0
-6.0 -74.0 -40.0
-4.0 -74.0 -40.0
-4.0 -76.0 -40.0
2.0 -76.0 -40.0
2.0 -74.0 -40.0
4.0 -74.0 -40.0
4.0 -62.0 -40.0
-4.0 -62.0 -40.0
#ROI ON SLICE 19 DEFINITION:
18 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -62.0 -38.0
-6.0 -74.0 -38.0
-4.0 -74.0 -38.0
-4.0 -76.0 -38.0
2.0 -76.0 -38.0
2.0 -74.0 -38.0
4.0 -74.0 -38.0
4.0 -62.0 -38.0
-6.0 -62.0 -38.0
#ROI ON SLICE 20 DEFINITION:
19 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -60.0 -36.0
-6.0 -66.0 -36.0
-4.0 -66.0 -36.0
-4.0 -74.0 -36.0
4.0 -74.0 -36.0
4.0 -72.0 -36.0
2.0 -72.0 -36.0
2.0 -70.0 -36.0
4.0 -70.0 -36.0
4.0 -60.0 -36.0
-6.0 -60.0 -36.0
#ROI ON SLICE 21 DEFINITION:
20 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -60.0 -34.0
-6.0 -64.0 -34.0
-4.0 -64.0 -34.0
-4.0 -70.0 -34.0
-2.0 -70.0 -34.0
-2.0 -72.0 -34.0
2.0 -72.0 -34.0
2.0 -70.0 -34.0
4.0 -70.0 -34.0
4.0 -60.0 -34.0
-6.0 -60.0 -34.0
#ROI ON SLICE 22 DEFINITION:
21 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
18 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -58.0 -32.0
-6.0 -60.0 -32.0
-8.0 -60.0 -32.0
-8.0 -62.0 -32.0
-6.0 -62.0 -32.0
-6.0 -64.0 -32.0
-4.0 -64.0 -32.0
-4.0 -68.0 -32.0
-2.0 -68.0 -32.0
-2.0 -70.0 -32.0
4.0 -70.0 -32.0
4.0 -62.0 -32.0
6.0 -62.0 -32.0
6.0 -60.0 -32.0
4.0 -60.0 -32.0
4.0 -58.0 -32.0
-6.0 -58.0 -32.0
#ROI ON SLICE 23 DEFINITION:
22 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -58.0 -30.0
-6.0 -60.0 -30.0
-8.0 -60.0 -30.0
-8.0 -66.0 -30.0
6.0 -66.0 -30.0
6.0 -60.0 -30.0
4.0 -60.0 -30.0
4.0 -58.0 -30.0
-6.0 -58.0 -30.0
#ROI ON SLICE 24 DEFINITION:
23 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
6 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-6.0 -60.0 -28.0
-6.0 -62.0 -28.0
4.0 -62.0 -28.0
4.0 -60.0 -28.0
-6.0 -60.0 -28.0
#VOI TIME LIST NUMBER 114 DEFINITION:
114 2 # voi_time_list_index voi_direction
Vermis_9_o # voi_name
1 true 255 255 0 false 1.0 false 137.0 -137.0 false false 0 1  # time_list_state removable_flag r_color g_color b_color is_tape_flag tape_width is_sector sector_start_angle sector_end_angle is_mask is_time_list first_volume_time number_of_sectors sec_1_start_angle sec_1_stop_angle .. sec_N_start_angle sec_N_stop_angle
false false # is_in_group is_mesh
1 # number_of_vois
#VOIS DEFINITION:
#TIME VOI NUMBER 115 DEFINITION:
115 # voi_index(1 ..)
115 # voi_code_(voi_index-1)
0 # voi_state_(0=OK)
true # is_voi_in_group
8 # number_of_rois
0.0 # time_in_seconds start_time: 0.0 end_time: 0.0
#ROIS DEFINITION:
#ROI ON SLICE 15 DEFINITION:
14 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
0.0 -58.0 -46.0
0.0 -62.0 -46.0
-2.0 -62.0 -46.0
-2.0 -64.0 -46.0
4.0 -64.0 -46.0
4.0 -62.0 -46.0
2.0 -62.0 -46.0
2.0 -58.0 -46.0
0.0 -58.0 -46.0
#ROI ON SLICE 16 DEFINITION:
15 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
0.0 -48.0 -44.0
0.0 -58.0 -44.0
-2.0 -58.0 -44.0
-2.0 -60.0 -44.0
-6.0 -60.0 -44.0
-6.0 -64.0 -44.0
4.0 -64.0 -44.0
4.0 -58.0 -44.0
2.0 -58.0 -44.0
2.0 -48.0 -44.0
0.0 -48.0 -44.0
#ROI ON SLICE 17 DEFINITION:
16 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-2.0 -48.0 -42.0
-2.0 -56.0 -42.0
-4.0 -56.0 -42.0
-4.0 -58.0 -42.0
-6.0 -58.0 -42.0
-6.0 -64.0 -42.0
4.0 -64.0 -42.0
4.0 -56.0 -42.0
2.0 -56.0 -42.0
2.0 -48.0 -42.0
-2.0 -48.0 -42.0
#ROI ON SLICE 18 DEFINITION:
17 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
22 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-2.0 -48.0 -40.0
-2.0 -52.0 -40.0
-4.0 -52.0 -40.0
-4.0 -56.0 -40.0
-6.0 -56.0 -40.0
-6.0 -58.0 -40.0
-8.0 -58.0 -40.0
-8.0 -62.0 -40.0
-6.0 -62.0 -40.0
-6.0 -64.0 -40.0
-4.0 -64.0 -40.0
-4.0 -62.0 -40.0
4.0 -62.0 -40.0
4.0 -60.0 -40.0
6.0 -60.0 -40.0
6.0 -58.0 -40.0
4.0 -58.0 -40.0
4.0 -54.0 -40.0
2.0 -54.0 -40.0
2.0 -48.0 -40.0
-2.0 -48.0 -40.0
#ROI ON SLICE 19 DEFINITION:
18 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
18 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-2.0 -50.0 -38.0
-2.0 -52.0 -38.0
-4.0 -52.0 -38.0
-4.0 -56.0 -38.0
-6.0 -56.0 -38.0
-6.0 -58.0 -38.0
-8.0 -58.0 -38.0
-8.0 -60.0 -38.0
-6.0 -60.0 -38.0
-6.0 -62.0 -38.0
6.0 -62.0 -38.0
6.0 -58.0 -38.0
4.0 -58.0 -38.0
4.0 -54.0 -38.0
2.0 -54.0 -38.0
2.0 -50.0 -38.0
-2.0 -50.0 -38.0
#ROI ON SLICE 20 DEFINITION:
19 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
14 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -52.0 -36.0
-4.0 -54.0 -36.0
-6.0 -54.0 -36.0
-6.0 -56.0 -36.0
-8.0 -56.0 -36.0
-8.0 -60.0 -36.0
6.0 -60.0 -36.0
6.0 -56.0 -36.0
4.0 -56.0 -36.0
4.0 -54.0 -36.0
2.0 -54.0 -36.0
2.0 -52.0 -36.0
-4.0 -52.0 -36.0
#ROI ON SLICE 21 DEFINITION:
20 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
10 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -52.0 -34.0
-4.0 -54.0 -34.0
-6.0 -54.0 -34.0
-6.0 -56.0 -34.0
-8.0 -56.0 -34.0
-8.0 -60.0 -34.0
6.0 -60.0 -34.0
6.0 -52.0 -34.0
-4.0 -52.0 -34.0
#ROI ON SLICE 22 DEFINITION:
21 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
14 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -52.0 -32.0
-4.0 -54.0 -32.0
-8.0 -54.0 -32.0
-8.0 -60.0 -32.0
-6.0 -60.0 -32.0
-6.0 -58.0 -32.0
4.0 -58.0 -32.0
4.0 -60.0 -32.0
6.0 -60.0 -32.0
6.0 -54.0 -32.0
4.0 -54.0 -32.0
4.0 -52.0 -32.0
-4.0 -52.0 -32.0
#VOI TIME LIST NUMBER 115 DEFINITION:
115 2 # voi_time_list_index voi_direction
Vermis_10_o # voi_name
1 true 100 100 250 false 1.0 false 137.0 -137.0 false false 0 1  # time_list_state removable_flag r_color g_color b_color is_tape_flag tape_width is_sector sector_start_angle sector_end_angle is_mask is_time_list first_volume_time number_of_sectors sec_1_start_angle sec_1_stop_angle .. sec_N_start_angle sec_N_stop_angle
false false # is_in_group is_mesh
1 # number_of_vois
#VOIS DEFINITION:
#TIME VOI NUMBER 116 DEFINITION:
116 # voi_index(1 ..)
116 # voi_code_(voi_index-1)
0 # voi_state_(0=OK)
true # is_voi_in_group
8 # number_of_rois
0.0 # time_in_seconds start_time: 0.0 end_time: 0.0
#ROIS DEFINITION:
#ROI ON SLICE 17 DEFINITION:
16 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -42.0 -42.0
-4.0 -46.0 -42.0
-2.0 -46.0 -42.0
-2.0 -48.0 -42.0
2.0 -48.0 -42.0
2.0 -46.0 -42.0
6.0 -46.0 -42.0
6.0 -44.0 -42.0
4.0 -44.0 -42.0
4.0 -42.0 -42.0
-4.0 -42.0 -42.0
#ROI ON SLICE 18 DEFINITION:
17 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -42.0 -40.0
-4.0 -46.0 -40.0
-2.0 -46.0 -40.0
-2.0 -48.0 -40.0
2.0 -48.0 -40.0
2.0 -46.0 -40.0
4.0 -46.0 -40.0
4.0 -44.0 -40.0
6.0 -44.0 -40.0
6.0 -42.0 -40.0
-4.0 -42.0 -40.0
#ROI ON SLICE 19 DEFINITION:
18 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
14 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-2.0 -42.0 -38.0
-2.0 -44.0 -38.0
-4.0 -44.0 -38.0
-4.0 -48.0 -38.0
-2.0 -48.0 -38.0
-2.0 -50.0 -38.0
2.0 -50.0 -38.0
2.0 -46.0 -38.0
4.0 -46.0 -38.0
4.0 -44.0 -38.0
6.0 -44.0 -38.0
6.0 -42.0 -38.0
-2.0 -42.0 -38.0
#ROI ON SLICE 20 DEFINITION:
19 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
8 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -44.0 -36.0
-4.0 -52.0 -36.0
2.0 -52.0 -36.0
2.0 -50.0 -36.0
4.0 -50.0 -36.0
4.0 -44.0 -36.0
-4.0 -44.0 -36.0
#ROI ON SLICE 21 DEFINITION:
20 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -44.0 -34.0
-4.0 -46.0 -34.0
-6.0 -46.0 -34.0
-6.0 -54.0 -34.0
-4.0 -54.0 -34.0
-4.0 -52.0 -34.0
6.0 -52.0 -34.0
6.0 -46.0 -34.0
4.0 -46.0 -34.0
4.0 -44.0 -34.0
-4.0 -44.0 -34.0
#ROI ON SLICE 22 DEFINITION:
21 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
18 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -46.0 -32.0
-4.0 -48.0 -32.0
-6.0 -48.0 -32.0
-6.0 -52.0 -32.0
-8.0 -52.0 -32.0
-8.0 -54.0 -32.0
-4.0 -54.0 -32.0
-4.0 -52.0 -32.0
4.0 -52.0 -32.0
4.0 -54.0 -32.0
6.0 -54.0 -32.0
6.0 -50.0 -32.0
4.0 -50.0 -32.0
4.0 -48.0 -32.0
2.0 -48.0 -32.0
2.0 -46.0 -32.0
-4.0 -46.0 -32.0
#ROI ON SLICE 23 DEFINITION:
22 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
12 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -48.0 -30.0
-4.0 -50.0 -30.0
-6.0 -50.0 -30.0
-6.0 -54.0 -30.0
6.0 -54.0 -30.0
6.0 -52.0 -30.0
4.0 -52.0 -30.0
4.0 -50.0 -30.0
2.0 -50.0 -30.0
2.0 -48.0 -30.0
-4.0 -48.0 -30.0
#ROI ON SLICE 24 DEFINITION:
23 1 # roi_slice_index number_of_contours
1 # roi_state_(1=FINISHED)
6 137.0 -137.0 false 0 " Contour 1 " # number_of_vertices [sector_start_angle sector_stop_angle] is_line_contour operation name VERTICES_DEFINITION_x_y_z_IN_MM:
-4.0 -50.0 -28.0
-4.0 -54.0 -28.0
4.0 -54.0 -28.0
4.0 -50.0 -28.0
-4.0 -50.0 -28.0
#
#END OF VOIS DEFINITION

import subprocess
import os
import sys
import platform

def get_hdbet_executable():
    """
    获取HD-BET可执行文件的路径
    """
    # 首先尝试在PATH中查找
    try:
        result = subprocess.run(["hd-bet", "--help"], capture_output=True, text=True)
        if result.returncode == 0:
            return "hd-bet"
    except FileNotFoundError:
        pass

    # 如果在PATH中找不到，尝试用户安装路径
    if platform.system() == "Windows":
        # Windows用户安装路径
        user_scripts_path = os.path.expanduser("~\\AppData\\Roaming\\Python\\Python312\\Scripts\\hd-bet.exe")
        if os.path.exists(user_scripts_path):
            return user_scripts_path
    else:
        # Linux/Mac用户安装路径
        user_scripts_path = os.path.expanduser("~/.local/bin/hd-bet")
        if os.path.exists(user_scripts_path):
            return user_scripts_path

    return None

def run_brain_extraction_hd_bet(input_image_path, output_image_path, device="cpu", verbose=True):
    """
    使用HD-BET对输入的NIfTI图像进行脑提取。

    参数:
    input_image_path (str): 完整的头部NIfTI图像文件路径。
    output_image_path (str): 提取出的大脑图像的保存路径。
    device (str): 使用的设备，"cpu" 或 "cuda"。默认为 "cpu"。
    verbose (bool): 是否显示详细输出。默认为 True。

    返回:
    bool: 如果成功则返回True，否则返回False。
    """
    print("--- 开始使用HD-BET进行脑提取 ---")

    # 检查输入文件是否存在
    if not os.path.exists(input_image_path):
        print(f"错误: 输入文件不存在: {input_image_path}")
        return False

    # 获取HD-BET可执行文件路径
    hdbet_exe = get_hdbet_executable()
    if hdbet_exe is None:
        print("错误: 无法找到HD-BET可执行文件。")
        print("请确保已正确安装HD-BET: pip install hd-bet")
        return False

    print(f"使用HD-BET可执行文件: {hdbet_exe}")

    # 构建要在终端中执行的命令
    # 格式: ["命令", "参数1", "值1", "参数2", "值2", ...]
    command = [
        hdbet_exe,
        "-i", input_image_path,
        "-o", output_image_path,
        "-device", device
    ]

    # 添加可选参数
    if device == "cpu":
        command.append("--disable_tta")  # CPU模式下禁用测试时增强以提高速度

    if verbose:
        command.append("--verbose")

    print(f"正在执行命令: {' '.join(command)}")

    try:
        # 创建输出目录（如果不存在）
        output_dir = os.path.dirname(output_image_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"创建输出目录: {output_dir}")

        # 使用subprocess.run执行命令
        # check=True: 如果命令返回非零退出码（表示错误），则会引发一个CalledProcessError异常。
        # capture_output=True: 捕获标准输出和标准错误。
        # text=True: 将捕获的输出解码为文本。
        print("开始执行HD-BET，这可能需要几分钟时间...")
        result = subprocess.run(command, check=True, capture_output=True, text=True)

        # 如果命令成功，打印一些输出信息
        print("HD-BET成功完成！")
        print("--- HD-BET输出信息 ---")
        print(result.stdout) # 打印标准输出
        print("----------------------")

        # 检查输出文件是否存在
        if os.path.exists(output_image_path):
            print(f"输出文件已生成: {output_image_path}")
            file_size = os.path.getsize(output_image_path) / (1024 * 1024)  # MB
            print(f"文件大小: {file_size:.2f} MB")
        else:
            print("警告: 输出文件未找到，但命令执行成功。")

        return True

    except FileNotFoundError:
        print("错误: HD-BET可执行文件未找到。")
        print("请确保您已经使用 'pip install hd-bet' 安装了它，并且Python环境配置正确。")
        return False

    except subprocess.CalledProcessError as e:
        # 如果命令执行失败 (check=True会触发这个)
        print("错误: HD-BET执行失败。")
        print(f"返回码: {e.returncode}")
        print("--- 错误信息 (stderr) ---")
        print(e.stderr) # 打印详细的错误输出，非常重要！
        print("--------------------------")
        if e.stdout:
            print("--- 标准输出 (stdout) ---")
            print(e.stdout)
            print("-------------------------")
        return False

    except Exception as e:
        print(f"未预期的错误: {e}")
        return False

def test_hdbet_installation():
    """
    测试HD-BET是否正确安装
    """
    print("--- 测试HD-BET安装 ---")
    hdbet_exe = get_hdbet_executable()
    if hdbet_exe is None:
        print("❌ HD-BET未找到")
        return False

    try:
        result = subprocess.run([hdbet_exe, "--help"], capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ HD-BET安装正确")
            print(f"可执行文件路径: {hdbet_exe}")
            return True
        else:
            print("❌ HD-BET执行失败")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 测试HD-BET时出错: {e}")
        return False

# --- 如何使用这个函数 ---
if __name__ == "__main__":
    # 首先测试HD-BET安装
    if not test_hdbet_installation():
        print("请先正确安装HD-BET后再运行此脚本。")
        sys.exit(1)

    # !! 重要 !! 请修改为您自己的路径
    # 这是您DICOM转NIfTI后得到的完整头部文件
    full_head_nii = "li_yunqiao_MRI/NIFTI_Output_Folder/Series.nii.gz"

    # 这是您希望保存脑提取结果的文件路径
    brain_only_nii = "li_yunqiao_MRI/NIFTI_Output_Folder/brain_only_hd-bet.nii.gz"

    # 调用函数执行脑提取
    # device="cpu" 适用于大多数情况，如果您有NVIDIA GPU且安装了CUDA，可以改为 "cuda"
    success = run_brain_extraction_hd_bet(full_head_nii, brain_only_nii, device="cpu", verbose=True)

    if success:
        print(f"\n✅ 脑提取成功！结果保存在: {brain_only_nii}")
        print("您现在可以将这个文件作为输入，用于后续的配准步骤。")
    else:
        print("\n❌ 脑提取失败。请检查上面的错误信息。")
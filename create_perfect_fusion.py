import SimpleITK as sitk
import numpy as np
import os

def create_perfect_fusion():
    """
    创建完美的融合图像：脑部图像作为灰度背景，AAL标签作为透明叠加
    """
    # 文件路径
    aal_path = "./PMOD-VOI/AAL-Merged/AAL-Merged.nii.gz"
    brain_path = "./output/test_MRI/registered_brain_hd-bet.nii.gz"
    output_path = "./output/test_MRI/perfect_fusion.nii.gz"

    # 检查文件是否存在
    if not os.path.exists(aal_path):
        print(f"错误: AAL模板文件不存在: {aal_path}")
        return
    if not os.path.exists(brain_path):
        print(f"错误: 配准后的脑部图像不存在: {brain_path}")
        return

    print("正在加载图像...")
    aal_image = sitk.ReadImage(aal_path)
    brain_image = sitk.ReadImage(brain_path)

    print("正在创建完美融合图像...")

    # 转换为numpy数组进行处理
    aal_array = sitk.GetArrayFromImage(aal_image)
    brain_array = sitk.GetArrayFromImage(brain_image)

    print(f"AAL图像形状: {aal_array.shape}")
    print(f"脑部图像形状: {brain_array.shape}")
    print(f"AAL标签范围: {aal_array.min()} - {aal_array.max()}")
    print(f"脑部强度范围: {brain_array.min()} - {brain_array.max()}")

    # 方法1: 保持原始AAL标签值，但在脑部区域外设置为特殊值
    fusion_array = aal_array.copy().astype(np.float32)

    # 创建脑部掩膜
    brain_mask = brain_array > (brain_array.max() * 0.1)  # 使用阈值创建脑部掩膜

    # 在脑部区域外，将AAL标签设置为0
    fusion_array[~brain_mask] = 0

    # 在脑部区域内但没有AAL标签的地方，使用脑部强度信息
    # 将脑部强度归一化到一个不与AAL标签冲突的范围
    brain_normalized = brain_array.copy().astype(np.float32)
    brain_normalized = (brain_normalized - brain_normalized.min()) / (brain_normalized.max() - brain_normalized.min())

    # 将脑部强度映射到100-200范围（避免与AAL标签1-71冲突）
    brain_intensity = brain_normalized * 100 + 100  # 范围100-200

    # 在有脑组织但没有AAL标签的区域，显示脑部强度
    brain_only_region = brain_mask & (aal_array == 0)
    fusion_array[brain_only_region] = brain_intensity[brain_only_region]

    print(f"融合图像强度范围: {fusion_array.min()} - {fusion_array.max()}")
    print(f"AAL区域像素数: {np.sum(aal_array > 0)}")
    print(f"脑组织像素数: {np.sum(brain_mask)}")
    print(f"重叠区域像素数: {np.sum(brain_mask & (aal_array > 0))}")
    print(f"脑部但无AAL标签区域: {np.sum(brain_only_region)}")

    # 转换回SimpleITK图像
    fusion_image = sitk.GetImageFromArray(fusion_array)
    fusion_image.CopyInformation(aal_image)  # 复制空间信息

    # 保存融合图像
    sitk.WriteImage(fusion_image, output_path)
    print(f"✅ 完美融合图像已保存至: {output_path}")

    return fusion_image

def create_weighted_fusion():
    """
    创建加权融合图像：在AAL区域内混合脑部和标签信息
    """
    print("\n正在创建加权融合图像...")

    # 文件路径
    aal_path = "./PMOD-VOI/AAL-Merged/AAL-Merged.nii.gz"
    brain_path = "./output/test_MRI/registered_brain_hd-bet.nii.gz"
    output_path = "./output/test_MRI/weighted_fusion.nii.gz"

    # 加载图像
    aal_image = sitk.ReadImage(aal_path)
    brain_image = sitk.ReadImage(brain_path)

    aal_array = sitk.GetArrayFromImage(aal_image)
    brain_array = sitk.GetArrayFromImage(brain_image)

    # 创建融合数组
    fusion_array = np.zeros_like(aal_array, dtype=np.float32)

    # 归一化脑部图像
    brain_normalized = brain_array.copy().astype(np.float32)
    brain_normalized = (brain_normalized - brain_normalized.min()) / (brain_normalized.max() - brain_normalized.min())

    # 创建掩膜
    brain_mask = brain_array > (brain_array.max() * 0.1)
    aal_mask = aal_array > 0

    # 在有AAL标签的区域：使用AAL标签值，但用脑部强度调制
    aal_region = aal_mask & brain_mask
    # 将脑部强度用作调制因子（0.5-1.0范围）
    brain_modulation = brain_normalized * 0.5 + 0.5
    fusion_array[aal_region] = aal_array[aal_region].astype(np.float32) * brain_modulation[aal_region]

    # 在只有脑组织的区域：显示脑部强度（映射到200-300范围）
    brain_only_region = brain_mask & (~aal_mask)
    fusion_array[brain_only_region] = brain_normalized[brain_only_region] * 100 + 200

    # 转换回SimpleITK图像
    fusion_image = sitk.GetImageFromArray(fusion_array)
    fusion_image.CopyInformation(aal_image)

    # 保存融合图像
    sitk.WriteImage(fusion_image, output_path)
    print(f"✅ 加权融合图像已保存至: {output_path}")

    return fusion_image

def create_rgb_fusion():
    """
    创建RGB彩色融合图像（如果支持的话）
    """
    print("\n正在创建RGB彩色融合图像...")
    
    # 文件路径
    aal_path = "./PMOD-VOI/AAL-Merged/AAL-Merged.nii.gz"
    brain_path = "./output/test_MRI/registered_brain_hd-bet.nii.gz"
    output_path = "./output/test_MRI/rgb_fusion.nii.gz"
    
    # 加载图像
    aal_image = sitk.ReadImage(aal_path)
    brain_image = sitk.ReadImage(brain_path)
    
    aal_array = sitk.GetArrayFromImage(aal_image)
    brain_array = sitk.GetArrayFromImage(brain_image)
    
    # 归一化脑部图像到0-255
    brain_normalized = brain_array.copy().astype(np.float32)
    brain_normalized = (brain_normalized - brain_normalized.min()) / (brain_normalized.max() - brain_normalized.min())
    brain_gray = (brain_normalized * 255).astype(np.uint8)
    
    # 创建RGB图像
    rgb_array = np.zeros((*aal_array.shape, 3), dtype=np.uint8)
    
    # 红色通道：脑部图像
    rgb_array[..., 0] = brain_gray
    
    # 绿色通道：AAL标签（归一化）
    aal_normalized = aal_array.copy().astype(np.float32)
    if aal_normalized.max() > 0:
        aal_normalized = (aal_normalized / aal_normalized.max() * 255).astype(np.uint8)
    rgb_array[..., 1] = aal_normalized
    
    # 蓝色通道：混合
    rgb_array[..., 2] = (brain_gray * 0.5 + aal_normalized * 0.5).astype(np.uint8)
    
    # 转换为SimpleITK向量图像
    rgb_image = sitk.GetImageFromArray(rgb_array, isVector=True)
    rgb_image.CopyInformation(aal_image)
    
    # 保存RGB图像
    sitk.WriteImage(rgb_image, output_path)
    print(f"✅ RGB融合图像已保存至: {output_path}")
    
    return rgb_image

if __name__ == "__main__":
    print("=== 创建完美融合图像 ===")

    try:
        # 创建完美融合图像
        perfect_fusion = create_perfect_fusion()

        # 创建加权融合图像
        weighted_fusion = create_weighted_fusion()

        # 创建RGB融合图像
        rgb_fusion = create_rgb_fusion()

        print("\n✅ 所有融合图像创建完成！")
        print("\n💡 使用建议:")
        print("- perfect_fusion.nii.gz: 保持AAL原始标签值，脑部区域显示解剖信息")
        print("- weighted_fusion.nii.gz: AAL标签用脑部强度调制，更好的视觉效果")
        print("- rgb_fusion.nii.gz: RGB彩色图像，可以直接显示彩色效果")
        print("- 在PMOD或其他医学图像查看器中，可以为这些图像设置不同的彩色映射表")

    except Exception as e:
        print(f"创建融合图像时出错: {e}")

import SimpleITK as sitk
import numpy as np
import matplotlib.pyplot as plt
import os

def load_and_display_images():
    """
    加载并显示配准和融合结果
    """
    # 文件路径
    fixed_path = "./PMOD-VOI/AAL-Merged/AAL-Merged.nii.gz"
    moving_path = "./li_yunqiao_MRI/NIFTI_Output_Folder/brain_only_hd-bet.nii.gz"
    registered_path = "./output/test_MRI/registered_brain_hd-bet.nii.gz"

    # 不同融合方法的路径
    fusion_paths = {
        'mask': "./output/test_MRI/fused_brain_aal_mask.nii.gz",
        'overlay': "./output/test_MRI/fused_brain_aal_overlay.nii.gz",
        'colormap': "./output/test_MRI/fused_brain_aal_colormap.nii.gz"
    }

    # 检查文件是否存在
    files_to_check = [fixed_path, moving_path, registered_path] + list(fusion_paths.values())
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在: {file_path}")
            return

    # 加载图像
    print("正在加载图像...")
    fixed_image = sitk.ReadImage(fixed_path)
    moving_image = sitk.ReadImage(moving_path)
    registered_image = sitk.ReadImage(registered_path)

    # 加载不同融合图像
    fusion_images = {}
    for method, path in fusion_paths.items():
        fusion_images[method] = sitk.ReadImage(path)

    # 转换为numpy数组
    fixed_array = sitk.GetArrayFromImage(fixed_image)
    registered_array = sitk.GetArrayFromImage(registered_image)

    fusion_arrays = {}
    for method, img in fusion_images.items():
        fusion_arrays[method] = sitk.GetArrayFromImage(img)

    # 选择中间切片进行显示
    z_slice = fixed_array.shape[0] // 2

    # 创建图像显示 - 2x3布局显示不同融合方法
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('脑部图像配准和不同融合方法对比', fontsize=16, fontweight='bold')

    # 第一行：原始图像和配准结果
    axes[0, 0].imshow(fixed_array[z_slice], cmap='nipy_spectral', vmin=0, vmax=71)
    axes[0, 0].set_title('AAL模板 (标签图像)')
    axes[0, 0].axis('off')

    # 重新采样移动图像到固定图像空间进行显示
    resampler = sitk.ResampleImageFilter()
    resampler.SetReferenceImage(fixed_image)
    resampler.SetInterpolator(sitk.sitkLinear)
    resampler.SetDefaultPixelValue(0)
    resampler.SetTransform(sitk.Transform())
    moving_resampled = resampler.Execute(moving_image)
    moving_resampled_array = sitk.GetArrayFromImage(moving_resampled)

    axes[0, 1].imshow(moving_resampled_array[z_slice], cmap='gray')
    axes[0, 1].set_title('原始脑部图像')
    axes[0, 1].axis('off')

    axes[0, 2].imshow(registered_array[z_slice], cmap='gray')
    axes[0, 2].set_title('配准后的脑部图像')
    axes[0, 2].axis('off')

    # 第二行：不同融合方法
    fusion_titles = {
        'mask': '掩膜方法\n(保留AAL标签)',
        'overlay': '叠加方法\n(边界叠加)',
        'colormap': '彩色映射\n(强度调制)'
    }

    fusion_cmaps = {
        'mask': 'nipy_spectral',
        'overlay': 'gray',
        'colormap': 'nipy_spectral'
    }

    for i, (method, array) in enumerate(fusion_arrays.items()):
        axes[1, i].imshow(array[z_slice], cmap=fusion_cmaps[method])
        axes[1, i].set_title(fusion_titles[method])
        axes[1, i].axis('off')

    plt.tight_layout()

    # 保存图像
    output_dir = "./output/test_MRI"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    plt.savefig(os.path.join(output_dir, 'fusion_comparison.png'), dpi=300, bbox_inches='tight')
    print(f"融合方法对比图已保存至: {os.path.join(output_dir, 'fusion_comparison.png')}")

    # 显示图像信息
    print("\n图像信息:")
    print(f"固定图像 (AAL): {fixed_image.GetSize()}, 间距: {fixed_image.GetSpacing()}")
    print(f"移动图像 (脑部): {moving_image.GetSize()}, 间距: {moving_image.GetSpacing()}")
    print(f"配准后图像: {registered_image.GetSize()}, 间距: {registered_image.GetSpacing()}")

    # 分析AAL标签保留情况
    print("\nAAL标签分析:")
    original_labels = np.unique(fixed_array[fixed_array > 0])
    print(f"原始AAL标签数量: {len(original_labels)} (范围: {original_labels.min()}-{original_labels.max()})")

    for method, array in fusion_arrays.items():
        preserved_labels = np.unique(array[array > 0])
        if method == 'mask':
            print(f"{method}方法保留标签: {len(preserved_labels)} (范围: {preserved_labels.min()}-{preserved_labels.max()})")

    plt.show()

def create_3d_slices_view():
    """
    创建三个方向的切片视图
    """
    fusion_path = "./output/test_MRI/fused_brain_aal.nii.gz"
    
    if not os.path.exists(fusion_path):
        print(f"错误: 融合图像不存在: {fusion_path}")
        return
    
    # 加载融合图像
    fusion_image = sitk.ReadImage(fusion_path)
    fusion_array = sitk.GetArrayFromImage(fusion_image)
    
    # 获取中间切片索引
    z_mid = fusion_array.shape[0] // 2  # 轴向
    y_mid = fusion_array.shape[1] // 2  # 冠状
    x_mid = fusion_array.shape[2] // 2  # 矢状
    
    # 创建三方向切片图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle('融合图像 - 三方向切片视图', fontsize=16, fontweight='bold')
    
    # 轴向切片 (Axial)
    axes[0].imshow(fusion_array[z_mid], cmap='gray')
    axes[0].set_title(f'轴向切片 (Z={z_mid})')
    axes[0].axis('off')
    
    # 冠状切片 (Coronal)
    axes[1].imshow(fusion_array[:, y_mid, :], cmap='gray')
    axes[1].set_title(f'冠状切片 (Y={y_mid})')
    axes[1].axis('off')
    
    # 矢状切片 (Sagittal)
    axes[2].imshow(fusion_array[:, :, x_mid], cmap='gray')
    axes[2].set_title(f'矢状切片 (X={x_mid})')
    axes[2].axis('off')
    
    plt.tight_layout()
    
    # 保存图像
    output_dir = "./output/test_MRI"
    plt.savefig(os.path.join(output_dir, 'fusion_3d_slices.png'), dpi=300, bbox_inches='tight')
    print(f"三方向切片图已保存至: {os.path.join(output_dir, 'fusion_3d_slices.png')}")
    
    plt.show()

if __name__ == "__main__":
    print("=== 脑部图像配准和融合结果可视化 ===")
    
    try:
        # 显示配准结果对比
        load_and_display_images()
        
        # 显示融合图像的三方向切片
        create_3d_slices_view()
        
        print("\n✅ 可视化完成！")
        
    except Exception as e:
        print(f"可视化过程中出错: {e}")
        print("请确保已安装 matplotlib: pip install matplotlib")
